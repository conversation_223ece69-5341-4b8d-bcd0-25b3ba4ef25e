import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FamilySwitcher } from '@/components/family/FamilySwitcher';
import { useFamily } from '@/hooks/useFamily';
import { useRouter } from 'next/navigation';

// Mock hooks
jest.mock('@/hooks/useFamily');
jest.mock('next/navigation');

const mockUseFamily = useFamily as jest.MockedFunction<typeof useFamily>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe('FamilySwitcher', () => {
  const mockPush = jest.fn();
  const mockSwitchFamily = jest.fn();

  const mockFamilies = [
    {
      id: 'family-1',
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON> rodzina',
      createdAt: new Date(),
      updatedAt: new Date(),
      ownerId: 'user-1',
    },
    {
      id: 'family-2',
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON> rodzina',
      createdAt: new Date(),
      updatedAt: new Date(),
      ownerId: 'user-2',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    });

    mockUseFamily.mockReturnValue({
      families: mockFamilies,
      currentFamily: mockFamilies[0],
      isLoading: false,
      error: null,
      switchFamily: mockSwitchFamily,
      refetch: jest.fn(),
    });
  });

  describe('renderowanie podstawowe', () => {
    it('powinien renderować przycisk z nazwą aktualnej rodziny', () => {
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(screen.getByText('Rodzina Kowalskich')).toBeInTheDocument();
    });

    it('powinien pokazać placeholder gdy brak aktualnej rodziny', () => {
      mockUseFamily.mockReturnValue({
        families: [],
        currentFamily: null,
        isLoading: false,
        error: null,
        switchFamily: mockSwitchFamily,
        refetch: jest.fn(),
      });

      render(<FamilySwitcher />);

      expect(screen.getByText('Wybierz rodzinę')).toBeInTheDocument();
    });
  });

  describe('stan ładowania', () => {
    it('powinien pokazać spinner podczas ładowania', () => {
      mockUseFamily.mockReturnValue({
        families: [],
        currentFamily: null,
        isLoading: true,
        error: null,
        switchFamily: mockSwitchFamily,
        refetch: jest.fn(),
      });

      render(<FamilySwitcher />);

      const loadingSpinner = screen.getByRole('status');
      expect(loadingSpinner).toBeInTheDocument();
    });
  });

  describe('lista rodzin', () => {
    it('powinien pokazać listę rodzin po kliknięciu', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByText('Rodzina Kowalskich')).toBeInTheDocument();
        expect(screen.getByText('Rodzina Nowakowych')).toBeInTheDocument();
      });
    });

    it('powinien oznaczyć aktualną rodzinę', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        const currentFamilyItem = screen.getByText('Rodzina Kowalskich').closest('[role="option"]');
        expect(currentFamilyItem).toHaveAttribute('aria-selected', 'true');
      });
    });
  });

  describe('przełączanie rodzin', () => {
    it('powinien przełączyć rodzinę po wybraniu z listy', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        const secondFamily = screen.getByText('Rodzina Nowakowych');
        expect(secondFamily).toBeInTheDocument();
      });

      const secondFamily = screen.getByText('Rodzina Nowakowych');
      await user.click(secondFamily);

      expect(mockSwitchFamily).toHaveBeenCalledWith('family-2');
    });

    it('powinien przekierować po przełączeniu rodziny', async () => {
      const user = userEvent.setup();
      mockSwitchFamily.mockResolvedValue(undefined);

      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        const secondFamily = screen.getByText('Rodzina Nowakowych');
        expect(secondFamily).toBeInTheDocument();
      });

      const secondFamily = screen.getByText('Rodzina Nowakowych');
      await user.click(secondFamily);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard');
      });
    });
  });

  describe('tworzenie nowej rodziny', () => {
    it('powinien pokazać opcję tworzenia nowej rodziny', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByText('Utwórz nową rodzinę')).toBeInTheDocument();
      });
    });

    it('powinien przekierować do strony tworzenia rodziny', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        const createButton = screen.getByText('Utwórz nową rodzinę');
        expect(createButton).toBeInTheDocument();
      });

      const createButton = screen.getByText('Utwórz nową rodzinę');
      await user.click(createButton);

      expect(mockPush).toHaveBeenCalledWith('/families/create');
    });
  });

  describe('obsługa błędów', () => {
    it('powinien obsłużyć błąd podczas przełączania rodziny', async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      mockSwitchFamily.mockRejectedValue(new Error('Switch error'));

      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      await user.click(button);

      await waitFor(() => {
        const secondFamily = screen.getByText('Rodzina Nowakowych');
        expect(secondFamily).toBeInTheDocument();
      });

      const secondFamily = screen.getByText('Rodzina Nowakowych');
      await user.click(secondFamily);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error switching family:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });

  describe('dostępność', () => {
    it('powinien mieć odpowiednie atrybuty ARIA', () => {
      render(<FamilySwitcher />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-haspopup', 'listbox');
      expect(button).toHaveAttribute('aria-expanded', 'false');
    });

    it('powinien być dostępny przez klawiaturę', async () => {
      const user = userEvent.setup();
      render(<FamilySwitcher />);

      // Nawigacja do przycisku
      await user.tab();
      const button = screen.getByRole('button');
      expect(button).toHaveFocus();

      // Otwarcie listy klawiszem Enter
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-expanded', 'true');
      });
    });
  });
});
