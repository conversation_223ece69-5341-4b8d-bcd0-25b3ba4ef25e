import { render, screen } from '@testing-library/react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

describe('LoadingSpinner', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować spinner z domyślnymi właściwościami', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toBeInTheDocument();
            expect(spinner).toHaveAttribute('aria-label', 'Loading');
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });
    });

    describe('rozmiary', () => {
        it('powinien renderować mały spinner', () => {
            render(<LoadingSpinner size="sm" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-4', 'w-4');
        });

        it('powinien renderować średni spinner (domyślny)', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-6', 'w-6');
        });

        it('powinien renderować duży spinner', () => {
            render(<LoadingSpinner size="lg" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-8', 'w-8');
        });
    });

    describe('warianty kolorów', () => {
        it('powinien renderować spinner z domyślnym kolorem', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner.firstChild).toHaveClass('text-primary');
        });

        it('powinien renderować spinner z białym kolorem', () => {
            render(<LoadingSpinner variant="white" />);

            const spinner = screen.getByRole('status');
            expect(spinner.firstChild).toHaveClass('text-white');
        });

        it('powinien renderować spinner z szarym kolorem', () => {
            render(<LoadingSpinner variant="muted" />);

            const spinner = screen.getByRole('status');
            expect(spinner.firstChild).toHaveClass('text-muted-foreground');
        });
    });

    describe('niestandardowe klasy CSS', () => {
        it('powinien dodać niestandardowe klasy CSS', () => {
            const customClass = 'my-custom-class';
            render(<LoadingSpinner className={customClass} />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass(customClass);
        });

        it('powinien zachować domyślne klasy przy dodawaniu niestandardowych', () => {
            render(<LoadingSpinner className="custom-class" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('flex', 'items-center', 'justify-center', 'custom-class');
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveAttribute('role', 'status');
            expect(spinner).toHaveAttribute('aria-label', 'Ładowanie...');
        });

        it('powinien być ukryty dla czytników ekranu gdy ma tekst alternatywny', () => {
            render(<LoadingSpinner text="Ładowanie danych" />);

            const spinner = screen.getByRole('status');
            const icon = spinner.querySelector('svg');
            expect(icon).toHaveAttribute('aria-hidden', 'true');
        });
    });

    describe('animacja', () => {
        it('powinien mieć klasę animacji', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            const icon = spinner.querySelector('svg');
            expect(icon).toHaveClass('animate-spin');
        });
    });
});
