import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeToggle } from '@/components/theme/ThemeToggle';
import { useTheme } from 'next-themes';

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: jest.fn(),
}));

const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>;

describe('ThemeToggle', () => {
  const mockSetTheme = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTheme.mockReturnValue({
      theme: 'light',
      setTheme: mockSetTheme,
      resolvedTheme: 'light',
      themes: ['light', 'dark', 'system'],
      systemTheme: 'light',
    });
  });

  describe('renderowanie podstawowe', () => {
    it('powinien renderować przycisk przełączania motywu', () => {
      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Przełącz motyw');
    });

    it('powinien pokazać ikonę słońca dla jasnego motywu', () => {
      mockUseTheme.mockReturnValue({
        theme: 'light',
        setTheme: mockSetTheme,
        resolvedTheme: 'light',
        themes: ['light', 'dark', 'system'],
        systemTheme: 'light',
      });

      render(<ThemeToggle />);

      // Sprawdź czy ikona słońca jest widoczna
      const sunIcon = document.querySelector('[data-testid="sun-icon"]') || 
                     document.querySelector('.lucide-sun');
      expect(sunIcon).toBeInTheDocument();
    });

    it('powinien pokazać ikonę księżyca dla ciemnego motywu', () => {
      mockUseTheme.mockReturnValue({
        theme: 'dark',
        setTheme: mockSetTheme,
        resolvedTheme: 'dark',
        themes: ['light', 'dark', 'system'],
        systemTheme: 'dark',
      });

      render(<ThemeToggle />);

      // Sprawdź czy ikona księżyca jest widoczna
      const moonIcon = document.querySelector('[data-testid="moon-icon"]') || 
                      document.querySelector('.lucide-moon');
      expect(moonIcon).toBeInTheDocument();
    });
  });

  describe('interakcje', () => {
    it('powinien przełączyć na ciemny motyw po kliknięciu z jasnego', async () => {
      const user = userEvent.setup();
      mockUseTheme.mockReturnValue({
        theme: 'light',
        setTheme: mockSetTheme,
        resolvedTheme: 'light',
        themes: ['light', 'dark', 'system'],
        systemTheme: 'light',
      });

      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      await user.click(button);

      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });

    it('powinien przełączyć na jasny motyw po kliknięciu z ciemnego', async () => {
      const user = userEvent.setup();
      mockUseTheme.mockReturnValue({
        theme: 'dark',
        setTheme: mockSetTheme,
        resolvedTheme: 'dark',
        themes: ['light', 'dark', 'system'],
        systemTheme: 'dark',
      });

      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      await user.click(button);

      expect(mockSetTheme).toHaveBeenCalledWith('light');
    });

    it('powinien obsłużyć motyw systemowy', async () => {
      const user = userEvent.setup();
      mockUseTheme.mockReturnValue({
        theme: 'system',
        setTheme: mockSetTheme,
        resolvedTheme: 'light',
        themes: ['light', 'dark', 'system'],
        systemTheme: 'light',
      });

      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      await user.click(button);

      // Przy motywie systemowym powinien przełączyć na przeciwny do resolvedTheme
      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });
  });

  describe('dostępność', () => {
    it('powinien mieć odpowiednie atrybuty ARIA', () => {
      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Przełącz motyw');
    });

    it('powinien być dostępny przez klawiaturę', async () => {
      const user = userEvent.setup();
      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      
      // Sprawdź czy można nawigować do przycisku
      await user.tab();
      expect(button).toHaveFocus();

      // Sprawdź czy można aktywować przycisk klawiszem Enter
      await user.keyboard('{Enter}');
      expect(mockSetTheme).toHaveBeenCalled();
    });
  });

  describe('edge cases', () => {
    it('powinien obsłużyć brak motywu', () => {
      mockUseTheme.mockReturnValue({
        theme: undefined,
        setTheme: mockSetTheme,
        resolvedTheme: undefined,
        themes: ['light', 'dark', 'system'],
        systemTheme: undefined,
      });

      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('powinien obsłużyć błąd w setTheme', async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      mockSetTheme.mockImplementation(() => {
        throw new Error('Theme error');
      });

      render(<ThemeToggle />);

      const button = screen.getByRole('button');
      await user.click(button);

      // Sprawdź czy błąd został obsłużony (nie powinien crashować)
      expect(button).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });
});
